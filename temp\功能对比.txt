// MSSQL存储过程：
USE [hlyy]
GO
/****** Object:  StoredProcedure [dbo].[fx_lnr_zs]    Script Date: 06/08/2025 11:59:03 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

ALTER PROCEDURE [dbo].[fx_lnr_zs] 
--fx_lnr_zs '06598907-df6d-42ca-8718-b76e8c71381e','380','455713','1958-03-20'
	----老年人分析
@code  nvarchar(50),
@akb020 nvarchar(20),
@yp_code nvarchar(20),
@csrq     nvarchar(20)
with recompile
AS
--return
declare @nl nvarchar(20)
declare @sda_id nvarchar(20)
declare @n_count_zy int
 select @sda_id=sda_id from t_byyydzb b where yp_code =@yp_code
 if @sda_id ='' or @sda_id is null
 begin 
 return 
 end
select @n_count_zy=COUNT(1) from ITF_HOS_DRUG where DRUG_CODE =@yp_code and ZX_FLAG ='3'
if @n_count_zy >0
begin 
return
end 


BEGIN
declare @ywa_name nvarchar(50)
select @ywa_name=DRUG_NAME from ITF_HOS_DRUG where DRUG_CODE =@yp_code

select  @nl=DATEDIFF(yy,@csrq,GETDATE())  

select @code, @ywa_name ywa, '' ywb,
--cast(a.jsbs  as varchar) as 
'1' wtlvlcode,
(select distinct d.wtlvl from wtlb d where d.wtlvlcode=a.jsbs) wtlvl,
case when a.jsbs='0' then  'RLT031' when a.jsbs='1' then 'RLT032' when a.jsbs='2' then 'RLT034' END AS wtcode,
case when a.jsbs='0' then  'LNRJJ' when a.jsbs='1' then 'LNRWT' when a.jsbs='2' then 'LNRJJTS'END  as wtsp,
case when a.jsbs='0' then  '特殊人群禁用' when a.jsbs='1' then '特殊人群慎用' when a.jsbs='2' then '特殊人群提示' END AS wtname,
case when a.jsbs='0' then  '特殊人群禁用' when a.jsbs='1' then '特殊人群慎用' when a.jsbs='2' then '特殊人群提示' END AS  title,
case when a.jsbs='0' then '说明书提示：'+cast(@ywa_name as varchar)+'老年人禁用！' when a.jsbs='1' then '说明书提示：'+cast(@ywa_name as varchar)+'老年人慎用！' end as detail,0,'老年人用药问题'
from t_sda_elder a
where a.sda_id=@sda_id
and a.jsbs <>'9'
and a.jsbs='0'
and age_min <=@nl

END













// MySQL存储过程：
CREATE DEFINER=`root`@`%` PROCEDURE `rms_fx_lnr`(
    IN p_Code VARCHAR(50),
    IN p_akb020 VARCHAR(20),
    IN p_yp_code VARCHAR(20),
    IN p_csrq VARCHAR(20)
)
    COMMENT '老年人用药分析存储过程'
main_block: BEGIN
		DECLARE v_nl INT;
		DECLARE v_sda_id VARCHAR(20);
		DECLARE v_n_count_zy INT;
		DECLARE v_ywa_name VARCHAR(50);
		
		-- 异常处理
		DECLARE CONTINUE HANDLER FOR SQLEXCEPTION
		BEGIN
				-- 如果出现异常，继续执行
				BEGIN END;
		END;
		
		-- 获取标准数据ID
		SELECT sda_id INTO v_sda_id 
		FROM rms_t_byyydzb 
		WHERE yp_code = p_yp_code
		LIMIT 1;
		
		-- 如果没有标准数据ID则返回
		IF v_sda_id = '' OR v_sda_id IS NULL THEN
				LEAVE main_block;
		END IF;
		
		-- 检查是否为中药
		SELECT COUNT(1) INTO v_n_count_zy 
		FROM rms_itf_hos_drug 
		WHERE DRUG_CODE = p_yp_code AND ZX_FLAG = '3';
		
		-- 如果是中药则返回
		IF v_n_count_zy > 0 THEN
				LEAVE main_block;
		END IF;
		
		-- 获取药品名称
		SELECT DRUG_NAME INTO v_ywa_name 
		FROM rms_itf_hos_drug 
		WHERE DRUG_CODE = p_yp_code
		LIMIT 1;
		
		-- 计算年龄（年数）
		SET v_nl = YEAR(CURDATE()) - YEAR(STR_TO_DATE(p_csrq, '%Y-%m-%d'));
		
		-- 老年人用药分析：插入分析结果
		INSERT INTO rms_t_pres_fx (
				Code, ywa, ywb, wtlvlcode, wtlvl, wtcode, wtsp, wtname, title, detail, flag, text
		)
		SELECT 
				p_Code,
				v_ywa_name as ywa,
				'' as ywb,
				'1' as wtlvlcode,
				(SELECT DISTINCT d.wtlvl FROM rms_wtlb d WHERE d.wtlvlcode = a.jsbs LIMIT 1) as wtlvl,
				CASE
						WHEN a.jsbs = '0' THEN 'RLT031'
						WHEN a.jsbs = '1' THEN 'RLT032'
						WHEN a.jsbs = '2' THEN 'RLT034'
				END as wtcode,
				CASE
						WHEN a.jsbs = '0' THEN 'LNRJJ'
						WHEN a.jsbs = '1' THEN 'LNRWT'
						WHEN a.jsbs = '2' THEN 'LNRJJTS'
				END as wtsp,
				CASE
						WHEN a.jsbs = '0' THEN '特殊人群禁用'
						WHEN a.jsbs = '1' THEN '特殊人群慎用'
						WHEN a.jsbs = '2' THEN '特殊人群提示'
				END as wtname,
				CASE
						WHEN a.jsbs = '0' THEN '老年人禁用药品'
						WHEN a.jsbs = '1' THEN '老年人慎用药品'
						WHEN a.jsbs = '2' THEN '老年人提示药品'
				END as title,
				CASE
						WHEN a.jsbs = '0' THEN CONCAT('说明书提示：', v_ywa_name, '老年人禁用！')
						WHEN a.jsbs = '1' THEN CONCAT('说明书提示：', v_ywa_name, '老年人慎用！')
						WHEN a.jsbs = '2' THEN CONCAT('说明书提示：', v_ywa_name, '老年人用药注意事项！')
				END as detail,
				0 as flag,
				'老年人用药' as text
		FROM rms_t_sda_elder a
		WHERE a.sda_id = v_sda_id
		AND a.jsbs <> '9'
		AND a.jsbs = '0'
		AND a.age_min <= v_nl;
END